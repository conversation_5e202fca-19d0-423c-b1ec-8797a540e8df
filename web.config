<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <rewrite>
            <rules>
                <rule name="ReverseProxyToNode" stopProcessing="true">
                    <match url="(.*)" />
                    <action type="Rewrite" url="http://localhost:3001/{R:1}" />
                </rule>
            </rules>
        </rewrite>
        <handlers>
            <add name="Reverse Proxy" path="index.js" verb="*" modules="iisnode" resourceType="File" />
            <add name="iisnode" path="index.js" verb="*" type="iisnode" resourceType="File" requireAccess="Script" preCondition="integratedMode" />
        </handlers>
    </system.webServer>
</configuration>